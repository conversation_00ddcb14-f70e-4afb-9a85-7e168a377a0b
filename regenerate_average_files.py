#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成平均值檔案
基於修復後的氣象站資料重新計算縣市平均值
"""

import os
import pandas as pd
from pathlib import Path
import shutil

# 氣象站對應縣市的映射表
STATION_TO_COUNTY = {
    # 彰化縣 (CHA)
    '72M700': 'CHA',
    
    # 嘉義市 (CYI)
    'G2L020': 'CYI',
    
    # 嘉義縣 (CYQ)
    '72M360': 'CYQ', 'CAJ050': 'CYQ', 'CAL110': 'CYQ', 'CAN130': 'CYQ',
    'CAG100': 'CYQ', 'CAH030': 'CYQ', 'CAN140': 'CYQ', 'CAQ030': 'CYQ',
    
    # 新竹縣 (HSZ) - 注意：這裡應該是新竹市
    '72N100': 'HSZ', '72N240': 'HSZ', 'A2N290': 'HSZ', 'B2N890': 'HSZ',
    
    # 花蓮縣 (HUA)
    '72T250': 'HUA',
    
    # 宜蘭縣 (ILN)
    '72U480': 'ILN',
    
    # 高雄市 (KHH)
    'U2H480': 'KHH', 'U2HA30': 'KHH', 'U2HA40': 'KHH', 'U2HA50': 'KHH',
    'E2H360': 'KHH', 'E2HA20': 'KHH',
    
    # 金門縣 (KIN)
    '82A750': 'KIN',
    
    # 連江縣 (LIE)
    '82C160': 'LIE',
    
    # 苗栗縣 (MIA)
    'K2E360': 'MIA', 'K2E710': 'MIA', 'K2F750': 'MIA',
    
    # 南投縣 (NTO)
    '72HA00': 'NTO',
    
    # 新北市 (NTP)
    '12Q970': 'NTP', '12Q980': 'NTP', '42HA10': 'NTP', 'B2Q810': 'NTP',
    
    # 澎湖縣 (PEN)
    '82H320': 'PEN', '82H840': 'PEN', '82S580': 'PEN',
    
    # 屏東縣 (PIF)
    'G2AI50': 'PIF', 'G2F820': 'PIF', 'G2M350': 'PIF', 'G2P820': 'PIF',
    'E2P980': 'PIF', 'E2P990': 'PIF', 'E2S960': 'PIF', 'E2S980': 'PIF',
    
    # 台南市 (TNN)
    'V2C250': 'TNN', 'V2C260': 'TNN', '72V140': 'TNN', 'A2C560': 'TNN',
    
    # 台東縣 (TTT)
    '72S200': 'TTT', '72S590': 'TTT',
    
    # 台中市 (TXG)
    '72C440': 'TXG', '72D080': 'TXG', '72D680': 'TXG', '72G600': 'TXG',
    
    # 桃園市 (TAO)
    '72AI40': 'TAO', '72Q010': 'TAO', 'B2E890': 'TAO',
    
    # 雲林縣 (YUN)
    '12J990': 'YUN', '72K220': 'YUN', 'A2K360': 'YUN', 'A2K630': 'YUN',
    'E2K600': 'YUN', 'V2K610': 'YUN', 'V2K620': 'YUN'
}

def collect_station_data():
    """收集所有氣象站資料"""
    print("=" * 60)
    print("收集修復後的氣象站資料")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    all_data = []
    
    # 遍歷所有氣象站資料夾
    for item in base_path.iterdir():
        if item.is_dir() and item.name in STATION_TO_COUNTY:
            station_code = item.name
            county_code = STATION_TO_COUNTY[station_code]
            
            print(f"處理氣象站：{station_code} -> {county_code}")
            
            # 讀取該氣象站的所有CSV檔案
            csv_files = list(item.glob("*.csv"))
            
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file)
                    
                    if len(df) > 0 and 'ObsTime' in df.columns:
                        # 添加氣象站和縣市資訊
                        df['Station'] = station_code
                        df['County'] = county_code
                        
                        all_data.append(df)
                        print(f"  {csv_file.name}: {len(df)} 行資料")
                    
                except Exception as e:
                    print(f"  讀取失敗 {csv_file.name}: {e}")
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"\n總共收集到 {len(combined_df)} 行資料")
        return combined_df
    else:
        print("沒有收集到任何資料")
        return None

def calculate_county_averages(combined_df):
    """計算縣市平均值"""
    print("\n" + "=" * 60)
    print("計算縣市平均值")
    print("=" * 60)
    
    # 數值欄位（需要計算平均值的欄位）
    numeric_columns = [
        'StnPres', 'SeaPres', 'StnPresMax', 'StnPresMin', 'Temperature', 
        'T Max', 'T Min', 'Td dew point', 'RH', 'RHMin', 'WS', 'WD', 
        'WSGust', 'WDGust', 'Precp', 'PrecpHour', 'PrecpMax10', 'PrecpMax60', 
        'SunShine', 'SunshineRate', 'GloblRad', 'TxSoil0cm', 'TxSoil5cm', 
        'TxSoil10cm', 'TxSoil20cm', 'TxSoil30cm', 'TxSoil50cm', 'TxSoil100cm'
    ]
    
    # 轉換數值欄位
    for col in numeric_columns:
        if col in combined_df.columns:
            combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')
    
    # 按縣市和日期分組計算平均值
    county_averages = combined_df.groupby(['County', 'ObsTime']).agg({
        **{col: 'mean' for col in numeric_columns if col in combined_df.columns},
        'typhoon': 'first',  # 颱風標記取第一個值
        'typhoon name': 'first'  # 颱風名稱取第一個值
    }).reset_index()
    
    # 四捨五入到小數點後1位
    for col in numeric_columns:
        if col in county_averages.columns:
            county_averages[col] = county_averages[col].round(1)
    
    # 添加city欄位
    county_averages.insert(0, 'city', county_averages['County'])
    
    print(f"計算完成：{len(county_averages)} 行平均值資料")
    
    return county_averages

def save_yearly_average_files(county_averages):
    """保存年度平均值檔案"""
    print("\n" + "=" * 60)
    print("保存年度平均值檔案")
    print("=" * 60)
    
    # 創建年度平均值資料夾
    base_path = Path("data/raw/weather/agricultural_weather_station")
    yearly_avg_path = base_path / "yearly_averages"
    yearly_avg_path.mkdir(exist_ok=True)
    
    # 提取年份
    county_averages['Year'] = county_averages['ObsTime'].str[:4]
    
    saved_files = []
    
    # 按縣市和年份分組保存
    for (county, year), group in county_averages.groupby(['County', 'Year']):
        print(f"處理 {county} {year}年：{len(group)} 行資料")
        
        # 創建年份資料夾
        year_folder = yearly_avg_path / year
        year_folder.mkdir(exist_ok=True)
        
        # 排序資料
        group_sorted = group.sort_values('ObsTime')
        
        # 移除輔助欄位
        group_final = group_sorted.drop(['County', 'Year'], axis=1)
        
        # 保存檔案
        filename = f"AVG_{county}_{year}.csv"
        file_path = year_folder / filename
        
        group_final.to_csv(file_path, index=False, encoding='utf-8')
        
        saved_files.append({
            'county': county,
            'year': year,
            'file_path': file_path,
            'data_rows': len(group_final)
        })
        
        print(f"  保存：{filename}")
    
    return saved_files

def verify_regenerated_files(saved_files):
    """驗證重新生成的檔案"""
    print("\n" + "=" * 60)
    print("驗證重新生成的檔案")
    print("=" * 60)
    
    # 檢查CHA 2023年的檔案，看看是否包含7月1日
    cha_2023_file = None
    for file_info in saved_files:
        if file_info['county'] == 'CHA' and file_info['year'] == '2023':
            cha_2023_file = file_info['file_path']
            break
    
    if cha_2023_file and cha_2023_file.exists():
        print(f"檢查測試檔案：{cha_2023_file}")
        
        df = pd.read_csv(cha_2023_file)
        
        # 檢查是否包含7月1日
        july_1_data = df[df['ObsTime'] == '2023-07-01']
        
        if not july_1_data.empty:
            print("✅ 找到7月1日資料！")
            print(f"   溫度：{july_1_data['Temperature'].iloc[0]}°C")
            print(f"   颱風：{july_1_data['typhoon'].iloc[0]}")
        else:
            print("❌ 仍然沒有找到7月1日資料")
        
        # 檢查總行數
        print(f"總資料行數：{len(df)}")
        
        # 檢查日期範圍
        first_date = df['ObsTime'].min()
        last_date = df['ObsTime'].max()
        print(f"日期範圍：{first_date} 到 {last_date}")
    
    # 統計總覽
    total_files = len(saved_files)
    total_rows = sum(f['data_rows'] for f in saved_files)
    
    print(f"\n生成統計：")
    print(f"總檔案數：{total_files}")
    print(f"總資料行數：{total_rows}")
    
    # 按年份統計
    by_year = {}
    for file_info in saved_files:
        year = file_info['year']
        if year not in by_year:
            by_year[year] = {'files': 0, 'rows': 0}
        by_year[year]['files'] += 1
        by_year[year]['rows'] += file_info['data_rows']
    
    for year in sorted(by_year.keys()):
        stats = by_year[year]
        print(f"{year}年：{stats['files']} 個檔案，{stats['rows']} 行資料")

def main():
    """主函數"""
    print("開始重新生成平均值檔案")
    
    # 步驟1：收集氣象站資料
    combined_df = collect_station_data()
    
    if combined_df is None:
        print("❌ 沒有收集到資料，無法繼續")
        return
    
    # 步驟2：計算縣市平均值
    county_averages = calculate_county_averages(combined_df)
    
    # 步驟3：保存年度平均值檔案
    saved_files = save_yearly_average_files(county_averages)
    
    # 步驟4：驗證結果
    verify_regenerated_files(saved_files)
    
    print("\n🎉 平均值檔案重新生成完成！")
    print("📁 新的平均值檔案位置：data/raw/weather/agricultural_weather_station/yearly_averages/")

if __name__ == "__main__":
    main()
