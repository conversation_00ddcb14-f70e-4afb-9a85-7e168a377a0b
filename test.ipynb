import pandas as pd
import os
import datetime

# 使用原始字串解決路徑問題
file_path = r'D:\Users\User\Desktop\Vegetable Price Prediction\data\raw\weather\agricultural_weather_station\CHA\72M700\72M700-2023-07.csv'

try:
    df = pd.read_csv(file_path)
    print(f"✅ 成功讀取檔案")
    print(f"檔案形狀: {df.shape}")
    print(f"欄位名稱: {list(df.columns)}")
    print(f"日期範圍: {df['ObsTime'].min()} 到 {df['ObsTime'].max()}")
    
    # 顯示前5行
    display(df.head())
    
    # 檢查是否包含7月1日資料
    july_1_data = df[df['ObsTime'] == '2023-07-01']
    if not july_1_data.empty:
        print(f"✅ 找到7月1日資料，溫度: {july_1_data['Temperature'].iloc[0]}°C")
    else:
        print("❌ 沒有找到7月1日資料")
        
except FileNotFoundError:
    print("❌ 檔案不存在，請檢查路徑")
except Exception as e:
    print(f"❌ 讀取錯誤: {e}")