#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查平均值檔案的資料缺失情況
生成詳細的缺失資料報告
"""

import os
import csv
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict

def generate_expected_dates(year, month):
    """生成指定年月的所有預期日期"""
    if month == 2:  # 二月
        if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):  # 閏年
            days_in_month = 29
        else:
            days_in_month = 28
    elif month in [4, 6, 9, 11]:  # 30天的月份
        days_in_month = 30
    else:  # 31天的月份
        days_in_month = 31
    
    expected_dates = []
    for day in range(1, days_in_month + 1):
        date_str = f"{year:04d}-{month:02d}-{day:02d}"
        expected_dates.append(date_str)
    
    return expected_dates

def check_file_missing_data(file_path):
    """檢查單個檔案的缺失資料"""
    missing_info = {
        'file_path': file_path,
        'missing_dates': [],
        'total_expected': 0,
        'total_actual': 0,
        'missing_count': 0
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 跳過表頭
            
            # 讀取所有資料行
            data_rows = list(reader)
            
        # 提取實際存在的日期
        actual_dates = set()
        for row in data_rows:
            if len(row) > 1 and row[1]:  # ObsTime欄位
                date_str = row[1]
                if len(date_str) == 10 and date_str.count('-') == 2:
                    actual_dates.add(date_str)
        
        # 從檔案名稱解析年份範圍
        filename = file_path.stem
        parts = filename.split('_')
        if len(parts) >= 3:
            year = int(parts[2])
            
            # 生成該年度所有預期日期
            all_expected_dates = set()
            for month in range(1, 13):
                month_dates = generate_expected_dates(year, month)
                all_expected_dates.update(month_dates)
            
            # 找出缺失的日期
            missing_dates = sorted(all_expected_dates - actual_dates)
            
            missing_info.update({
                'missing_dates': missing_dates,
                'total_expected': len(all_expected_dates),
                'total_actual': len(actual_dates),
                'missing_count': len(missing_dates)
            })
            
    except Exception as e:
        missing_info['error'] = str(e)
    
    return missing_info

def analyze_missing_patterns(missing_dates):
    """分析缺失日期的模式"""
    if not missing_dates:
        return []
    
    patterns = []
    current_start = None
    current_end = None
    
    for i, date_str in enumerate(missing_dates):
        try:
            current_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            
            if current_start is None:
                current_start = current_date
                current_end = current_date
            else:
                # 檢查是否連續
                if current_date == current_end + timedelta(days=1):
                    current_end = current_date
                else:
                    # 結束當前連續區間
                    if current_start == current_end:
                        patterns.append(f"{current_start}")
                    else:
                        patterns.append(f"{current_start} 到 {current_end}")
                    
                    # 開始新的區間
                    current_start = current_date
                    current_end = current_date
        except:
            continue
    
    # 處理最後一個區間
    if current_start is not None:
        if current_start == current_end:
            patterns.append(f"{current_start}")
        else:
            patterns.append(f"{current_start} 到 {current_end}")
    
    return patterns

def check_all_average_files():
    """檢查所有平均值檔案"""
    print("=" * 60)
    print("檢查所有平均值檔案的資料缺失情況")
    print("=" * 60)
    
    yearly_avg_path = Path("data/raw/weather/agricultural_weather_station/yearly_averages")
    
    if not yearly_avg_path.exists():
        print("找不到yearly_averages資料夾")
        return {}
    
    all_missing_info = {}
    total_files = 0
    files_with_missing = 0
    
    # 按年份處理
    for year_dir in sorted(yearly_avg_path.iterdir()):
        if year_dir.is_dir():
            year = year_dir.name
            print(f"\n檢查 {year} 年...")
            
            year_missing_info = {}
            
            # 處理該年度的所有縣市檔案
            avg_files = list(year_dir.glob("AVG_*.csv"))
            
            for avg_file in sorted(avg_files):
                total_files += 1
                
                # 從檔案名稱提取縣市代號
                filename = avg_file.stem
                parts = filename.split('_')
                if len(parts) >= 2:
                    county_code = parts[1]
                    
                    print(f"  檢查 {county_code}...")
                    
                    missing_info = check_file_missing_data(avg_file)
                    
                    if missing_info['missing_count'] > 0:
                        files_with_missing += 1
                        year_missing_info[county_code] = missing_info
                        
                        print(f"    發現 {missing_info['missing_count']} 個缺失日期")
                    else:
                        print(f"    無缺失資料")
            
            if year_missing_info:
                all_missing_info[year] = year_missing_info
    
    print(f"\n檢查完成：")
    print(f"總檔案數：{total_files}")
    print(f"有缺失資料的檔案：{files_with_missing}")
    print(f"缺失比例：{files_with_missing/total_files*100:.1f}%")
    
    return all_missing_info

def generate_missing_data_report(all_missing_info):
    """生成缺失資料報告"""
    report_path = Path("data/raw/weather/agricultural_weather_station/yearly_averages/missing_data_report.txt")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("氣象平均值檔案缺失資料報告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if not all_missing_info:
            f.write("🎉 所有平均值檔案都沒有缺失資料！\n")
            return report_path
        
        # 統計總覽
        total_files_with_missing = sum(len(year_data) for year_data in all_missing_info.values())
        total_missing_days = sum(
            info['missing_count'] 
            for year_data in all_missing_info.values() 
            for info in year_data.values()
        )
        
        f.write(f"總覽：\n")
        f.write(f"有缺失資料的檔案數：{total_files_with_missing} 個\n")
        f.write(f"總缺失天數：{total_missing_days} 天\n\n")
        
        # 按年份詳細報告
        for year in sorted(all_missing_info.keys()):
            year_data = all_missing_info[year]
            f.write(f"{year} 年缺失資料詳情：\n")
            f.write("-" * 30 + "\n")
            
            year_missing_days = sum(info['missing_count'] for info in year_data.values())
            f.write(f"該年度缺失天數：{year_missing_days} 天\n")
            f.write(f"有問題的縣市：{len(year_data)} 個\n\n")
            
            # 按縣市報告
            for county_code in sorted(year_data.keys()):
                info = year_data[county_code]
                f.write(f"縣市：{county_code}\n")
                f.write(f"檔案：{info['file_path'].name}\n")
                f.write(f"預期天數：{info['total_expected']} 天\n")
                f.write(f"實際天數：{info['total_actual']} 天\n")
                f.write(f"缺失天數：{info['missing_count']} 天\n")
                f.write(f"缺失比例：{info['missing_count']/info['total_expected']*100:.1f}%\n")
                
                # 分析缺失模式
                patterns = analyze_missing_patterns(info['missing_dates'])
                if patterns:
                    f.write(f"缺失時間段：\n")
                    for pattern in patterns:
                        f.write(f"  - {pattern}\n")
                
                f.write("\n")
            
            f.write("\n")
        
        # 詳細的缺失日期列表
        f.write("詳細缺失日期列表：\n")
        f.write("=" * 30 + "\n")
        
        for year in sorted(all_missing_info.keys()):
            year_data = all_missing_info[year]
            
            for county_code in sorted(year_data.keys()):
                info = year_data[county_code]
                
                if info['missing_dates']:
                    f.write(f"\n{county_code} ({year})：\n")
                    
                    # 按月份分組顯示
                    dates_by_month = defaultdict(list)
                    for date_str in info['missing_dates']:
                        month = date_str[:7]  # YYYY-MM
                        dates_by_month[month].append(date_str)
                    
                    for month in sorted(dates_by_month.keys()):
                        dates = dates_by_month[month]
                        f.write(f"  {month}: {len(dates)} 天缺失\n")
                        
                        # 顯示具體日期（每行最多10個）
                        for i in range(0, len(dates), 10):
                            batch = dates[i:i+10]
                            f.write(f"    {', '.join([d.split('-')[2] for d in batch])}\n")
    
    return report_path

def main():
    """主函數"""
    print("開始檢查平均值檔案的資料缺失情況")
    
    # 檢查所有檔案
    all_missing_info = check_all_average_files()
    
    # 生成報告
    report_path = generate_missing_data_report(all_missing_info)
    
    print(f"\n📋 缺失資料報告已生成：{report_path}")
    
    if all_missing_info:
        print(f"⚠️ 發現資料缺失問題，請查看報告了解詳情")
    else:
        print(f"✅ 所有平均值檔案都沒有缺失資料")

if __name__ == "__main__":
    main()
