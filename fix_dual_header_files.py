#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復雙表頭檔案
處理同時包含中文表頭和英文表頭的檔案，將其轉換為標準格式
"""

import os
import csv
import shutil
from pathlib import Path
from datetime import datetime

# 颱風資料
TYPHOON_DATA = {
    'Weipa': ('2025-07-18', '2025-07-19'),
    'Dana<PERSON>': ('2025-07-05', '2025-07-07'),
    'Usagi': ('2024-11-14', '2024-11-16'),
    'Kong-rey': ('2024-10-29', '2024-11-01'),
    '<PERSON><PERSON>': ('2024-09-29', '2024-10-04'),
    '<PERSON><PERSON><PERSON>': ('2024-07-22', '2024-07-26'),
    'Koinu': ('2023-10-02', '2023-10-06'),
    'Haikui': ('2023-09-01', '2023-09-05'),
    'Saola': ('2023-08-28', '2023-08-31'),
    'Khanun': ('2023-08-01', '2023-08-04'),
    'Doksuri': ('2023-07-24', '2023-07-28')
}

# 標準英文表頭
STANDARD_HEADERS = [
    "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
    "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
    "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
    "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
    "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
    "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
    "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
    "typhoon", "typhoon name"
]

def get_typhoon_info(date_str):
    """根據日期判斷是否有颱風及颱風名稱"""
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except:
        return 'N', ''
    
    for typhoon_name, (start_str, end_str) in TYPHOON_DATA.items():
        start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_str, '%Y-%m-%d').date()
        
        if start_date <= date <= end_date:
            return 'Y', typhoon_name
    
    return 'N', ''

def is_chinese_header(line):
    """檢查是否為中文表頭"""
    return any('\u4e00' <= char <= '\u9fff' for char in line)

def fix_dual_header_file(file_path):
    """修復雙表頭檔案"""
    print(f"處理檔案：{file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            print(f"  檔案行數不足：{len(lines)}")
            return False
        
        # 分析檔案結構
        first_line = lines[0].strip()
        second_line = lines[1].strip()
        
        # 檢查是否為雙表頭格式
        if is_chinese_header(first_line) and not is_chinese_header(second_line):
            print(f"  發現雙表頭格式")
            
            # 解析英文表頭
            try:
                reader = csv.reader([second_line])
                english_headers = next(reader)
                print(f"  英文表頭欄位數：{len(english_headers)}")
                
                # 只保留前37個標準欄位
                standard_data_headers = english_headers[:37]
                
                # 處理資料行（如果有的話）
                data_lines = []
                for i, line in enumerate(lines[2:], start=3):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        reader = csv.reader([line])
                        row = list(next(reader))
                        
                        # 檢查是否為有效的資料行（第一個欄位是日期）
                        if len(row) > 0 and len(row[0]) == 10 and row[0].count('-') == 2:
                            try:
                                datetime.strptime(row[0], '%Y-%m-%d')
                                
                                # 只保留前37個欄位
                                clean_row = row[:37]
                                while len(clean_row) < 37:
                                    clean_row.append('')
                                
                                # 添加颱風資訊
                                typhoon_flag, typhoon_name = get_typhoon_info(clean_row[0])
                                clean_row.append(typhoon_flag)
                                clean_row.append(typhoon_name)
                                
                                data_lines.append(clean_row)
                                
                            except ValueError:
                                continue
                    except:
                        continue
                
                print(f"  找到 {len(data_lines)} 行有效資料")
                
                # 寫入修復後的檔案
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    
                    # 寫入標準英文表頭
                    writer.writerow(STANDARD_HEADERS)
                    
                    # 寫入資料行
                    for row in data_lines:
                        writer.writerow(row)
                
                print(f"  修復完成：{len(data_lines)} 行資料")
                return True
                
            except Exception as e:
                print(f"  處理英文表頭失敗：{e}")
                return False
        else:
            print(f"  不是雙表頭格式")
            return False
            
    except Exception as e:
        print(f"  讀取檔案失敗：{e}")
        return False

def move_fixed_files_back():
    """將修復好的檔案移回原位置"""
    print("\n" + "=" * 60)
    print("將修復好的檔案移回原位置")
    print("=" * 60)
    
    exception_path = Path("data/raw/weather/agricultural_weather_station/exception_files")
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    moved_back_count = 0
    
    # 遍歷異常資料夾中的所有檔案
    for file_path in exception_path.rglob("*.csv"):
        if file_path.name == "exception_files_report.txt":
            continue
        
        # 計算原始位置
        relative_path = file_path.relative_to(exception_path)
        original_path = base_path / relative_path
        
        # 確保目標資料夾存在
        original_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 移動檔案
        print(f"移回：{relative_path}")
        shutil.move(str(file_path), str(original_path))
        moved_back_count += 1
    
    print(f"\n移回完成：{moved_back_count} 個檔案")
    return moved_back_count

def clean_empty_exception_folders():
    """清理空的異常資料夾"""
    exception_path = Path("data/raw/weather/agricultural_weather_station/exception_files")
    
    # 刪除空的子資料夾
    for item in exception_path.rglob("*"):
        if item.is_dir() and not any(item.iterdir()):
            print(f"刪除空資料夾：{item.relative_to(exception_path)}")
            item.rmdir()

def main():
    """主函數"""
    print("開始修復雙表頭檔案")
    print("=" * 60)
    
    exception_path = Path("data/raw/weather/agricultural_weather_station/exception_files")
    
    if not exception_path.exists():
        print("異常資料夾不存在")
        return
    
    # 統計
    fixed_count = 0
    total_count = 0
    
    # 處理所有異常檔案
    for file_path in exception_path.rglob("*.csv"):
        if file_path.name == "exception_files_report.txt":
            continue
        
        total_count += 1
        if fix_dual_header_file(file_path):
            fixed_count += 1
    
    print(f"\n修復統計：")
    print(f"總檔案數：{total_count}")
    print(f"修復成功：{fixed_count}")
    print(f"修復失敗：{total_count - fixed_count}")
    
    if fixed_count > 0:
        # 將修復好的檔案移回原位置
        moved_back_count = move_fixed_files_back()
        
        # 清理空資料夾
        clean_empty_exception_folders()
        
        print(f"\n🎉 修復完成！")
        print(f"✅ {fixed_count} 個檔案已修復並移回原位置")
        
        # 檢查是否還有異常檔案
        remaining_files = list(exception_path.rglob("*.csv"))
        remaining_files = [f for f in remaining_files if f.name != "exception_files_report.txt"]
        
        if len(remaining_files) == 0:
            print("✅ 所有異常檔案都已修復！")
        else:
            print(f"⚠️ 還有 {len(remaining_files)} 個檔案需要手動處理")
    else:
        print("❌ 沒有檔案被修復")

if __name__ == "__main__":
    main()
