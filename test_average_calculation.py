#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試平均值計算邏輯
檢查當有氣象站資料缺失時，pandas如何計算平均值
"""

import pandas as pd
import numpy as np

def test_pandas_mean_behavior():
    """測試pandas mean函數對缺失值的處理"""
    print("=" * 60)
    print("測試 pandas mean() 函數對缺失值的處理")
    print("=" * 60)
    
    # 測試案例1：3個氣象站，其中1個有缺失
    print("\n測試案例1：3個氣象站，其中1個有缺失")
    print("-" * 40)
    
    data1 = {
        'ObsTime': ['2023-07-01', '2023-07-01', '2023-07-01'],
        'Station': ['A', 'B', 'C'],
        'Temperature': [25.0, 30.0, np.nan],  # C站缺失
        'Humidity': [80.0, 70.0, 60.0]       # 都有資料
    }
    
    df1 = pd.DataFrame(data1)
    print("原始資料：")
    print(df1)
    
    # 按日期分組計算平均值
    avg1 = df1.groupby('ObsTime').agg({
        'Temperature': 'mean',
        'Humidity': 'mean'
    }).reset_index()
    
    print("\n平均值結果：")
    print(avg1)
    print(f"Temperature平均值：{avg1['Temperature'].iloc[0]} (應該是 (25+30)/2 = 27.5)")
    print(f"Humidity平均值：{avg1['Humidity'].iloc[0]} (應該是 (80+70+60)/3 = 70.0)")
    
    # 測試案例2：3個氣象站，其中2個有缺失
    print("\n\n測試案例2：3個氣象站，其中2個有缺失")
    print("-" * 40)
    
    data2 = {
        'ObsTime': ['2023-07-01', '2023-07-01', '2023-07-01'],
        'Station': ['A', 'B', 'C'],
        'Temperature': [25.0, np.nan, np.nan],  # B、C站缺失
        'Humidity': [80.0, 70.0, 60.0]         # 都有資料
    }
    
    df2 = pd.DataFrame(data2)
    print("原始資料：")
    print(df2)
    
    avg2 = df2.groupby('ObsTime').agg({
        'Temperature': 'mean',
        'Humidity': 'mean'
    }).reset_index()
    
    print("\n平均值結果：")
    print(avg2)
    print(f"Temperature平均值：{avg2['Temperature'].iloc[0]} (應該是 25.0，只有A站有資料)")
    print(f"Humidity平均值：{avg2['Humidity'].iloc[0]} (應該是 (80+70+60)/3 = 70.0)")
    
    # 測試案例3：3個氣象站，全部缺失
    print("\n\n測試案例3：3個氣象站，全部缺失")
    print("-" * 40)
    
    data3 = {
        'ObsTime': ['2023-07-01', '2023-07-01', '2023-07-01'],
        'Station': ['A', 'B', 'C'],
        'Temperature': [np.nan, np.nan, np.nan],  # 全部缺失
        'Humidity': [80.0, 70.0, 60.0]           # 都有資料
    }
    
    df3 = pd.DataFrame(data3)
    print("原始資料：")
    print(df3)
    
    avg3 = df3.groupby('ObsTime').agg({
        'Temperature': 'mean',
        'Humidity': 'mean'
    }).reset_index()
    
    print("\n平均值結果：")
    print(avg3)
    print(f"Temperature平均值：{avg3['Temperature'].iloc[0]} (應該是 NaN)")
    print(f"Humidity平均值：{avg3['Humidity'].iloc[0]} (應該是 (80+70+60)/3 = 70.0)")

def test_real_county_example():
    """測試實際縣市的例子"""
    print("\n\n" + "=" * 60)
    print("測試實際縣市例子")
    print("=" * 60)
    
    # 模擬CYQ縣市有8個氣象站的情況
    print("\n模擬CYQ縣市（8個氣象站）在某一天的情況：")
    print("-" * 50)
    
    data = {
        'ObsTime': ['2023-07-15'] * 8,
        'Station': ['72M360', 'CAJ050', 'CAL110', 'CAN130', 'CAG100', 'CAH030', 'CAN140', 'CAQ030'],
        'Temperature': [28.5, 29.0, np.nan, 27.8, 28.2, np.nan, 29.5, 28.0],  # 2個站缺失
        'Humidity': [75.0, 80.0, 78.0, 82.0, 76.0, 79.0, 77.0, 81.0]         # 都有資料
    }
    
    df = pd.DataFrame(data)
    print("8個氣象站的資料：")
    print(df)
    
    avg = df.groupby('ObsTime').agg({
        'Temperature': 'mean',
        'Humidity': 'mean'
    }).reset_index()
    
    print("\n平均值結果：")
    print(avg)
    
    # 手動計算驗證
    valid_temps = [28.5, 29.0, 27.8, 28.2, 29.5, 28.0]  # 6個有效值
    manual_temp_avg = sum(valid_temps) / len(valid_temps)
    
    valid_humidity = [75.0, 80.0, 78.0, 82.0, 76.0, 79.0, 77.0, 81.0]  # 8個有效值
    manual_humidity_avg = sum(valid_humidity) / len(valid_humidity)
    
    print(f"\n手動計算驗證：")
    print(f"Temperature: {manual_temp_avg:.1f} (6個有效值的平均)")
    print(f"Humidity: {manual_humidity_avg:.1f} (8個有效值的平均)")
    print(f"pandas結果: Temperature={avg['Temperature'].iloc[0]:.1f}, Humidity={avg['Humidity'].iloc[0]:.1f}")

def check_actual_county_data():
    """檢查實際的縣市資料"""
    print("\n\n" + "=" * 60)
    print("檢查實際的縣市平均值檔案")
    print("=" * 60)
    
    import os
    from pathlib import Path
    
    # 檢查CYQ縣市的實際資料
    cyq_path = Path("data/raw/weather/agricultural_weather_station/CYQ")
    
    if cyq_path.exists():
        print(f"\nCYQ縣市有以下氣象站：")
        station_dirs = [d for d in cyq_path.iterdir() if d.is_dir()]
        for i, station_dir in enumerate(station_dirs, 1):
            print(f"  {i}. {station_dir.name}")
        
        print(f"\n總共 {len(station_dirs)} 個氣象站")
        
        # 檢查某個特定日期的資料
        test_date = "2023-07-15"
        print(f"\n檢查 {test_date} 的資料：")
        
        station_data = []
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*2023-07*.csv"))
            if csv_files:
                csv_file = csv_files[0]
                try:
                    df = pd.read_csv(csv_file)
                    if 'ObsTime' in df.columns:
                        day_data = df[df['ObsTime'] == test_date]
                        if not day_data.empty:
                            temp_val = day_data['Temperature'].iloc[0] if 'Temperature' in day_data.columns else 'N/A'
                            station_data.append({
                                'Station': station_dir.name,
                                'Temperature': temp_val
                            })
                except:
                    pass
        
        if station_data:
            print("各氣象站的溫度資料：")
            for data in station_data:
                print(f"  {data['Station']}: {data['Temperature']}")
        else:
            print("無法讀取到該日期的資料")
    else:
        print("找不到CYQ縣市資料夾")

def main():
    """主函數"""
    print("開始測試平均值計算邏輯")
    
    # 測試pandas的行為
    test_pandas_mean_behavior()
    
    # 測試實際例子
    test_real_county_example()
    
    # 檢查實際資料
    check_actual_county_data()
    
    print("\n\n" + "=" * 60)
    print("結論")
    print("=" * 60)
    print("pandas的mean()函數會自動忽略NaN值：")
    print("- 如果3個氣象站中1個缺失，會用2個有效值計算平均 (除以2)")
    print("- 如果3個氣象站中2個缺失，會用1個有效值作為結果 (除以1)")
    print("- 如果3個氣象站全部缺失，結果為NaN")
    print("這是統計學上正確的做法，確保平均值代表有效觀測值的真實平均。")

if __name__ == "__main__":
    main()
