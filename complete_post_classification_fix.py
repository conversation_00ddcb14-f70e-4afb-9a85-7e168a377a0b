#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分類修正後的完整修復和驗證腳本
1. 修正平均值檔案名稱
2. 驗證所有檔案完整性
3. 生成完整性報告
"""

import os
import shutil
from pathlib import Path
import csv

def fix_average_file_names():
    """修正平均值檔案名稱"""
    print("=" * 60)
    print("修正平均值檔案名稱")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 需要修正的縣市對應表
    corrections = {
        'NTP': 'NWT',  # 新北市：資料夾NTP，但檔案名稱是NWT_*.csv
        'TAO': 'TYN',  # 桃園市：資料夾TAO，但檔案名稱是TYN_*.csv
    }
    
    fixed_count = 0
    
    for correct_code, old_code in corrections.items():
        county_dir = base_path / correct_code
        
        if county_dir.exists():
            print(f"\n處理縣市：{correct_code}")
            
            # 找到所有需要重新命名的平均值檔案
            old_pattern_files = list(county_dir.glob(f"{old_code}_*.csv"))
            
            for old_file in old_pattern_files:
                # 生成新的檔案名稱
                old_name = old_file.name
                new_name = old_name.replace(f"{old_code}_", f"{correct_code}_")
                new_file = county_dir / new_name
                
                if not new_file.exists():
                    print(f"  重新命名：{old_name} → {new_name}")
                    old_file.rename(new_file)
                    fixed_count += 1
                else:
                    print(f"  目標檔案已存在，跳過：{new_name}")
        else:
            print(f"縣市資料夾不存在：{correct_code}")
    
    print(f"\n平均值檔案名稱修正完成：{fixed_count} 個檔案")
    return fixed_count

def verify_county_structure():
    """驗證縣市結構完整性"""
    print("\n" + "=" * 60)
    print("驗證縣市結構完整性")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 正確的縣市代號列表
    expected_counties = [
        'CHA', 'CYI', 'CYQ', 'HSC', 'HSZ', 'HUA', 'ILN', 'KEE', 
        'KHH', 'KIN', 'LIE', 'MIA', 'NTO', 'NTP', 'PEN', 'PIF', 
        'TAO', 'TNN', 'TPE', 'TTT', 'TXG', 'YUN'
    ]
    
    # 檢查每個縣市
    county_status = {}
    
    for county in expected_counties:
        county_dir = base_path / county
        empty_county_dir = base_path / f"{county}(empty)"
        
        if county_dir.exists():
            # 有資料的縣市
            station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
            avg_files = list(county_dir.glob(f"{county}_*.csv"))
            
            county_status[county] = {
                'status': 'has_data',
                'stations': len(station_dirs),
                'avg_files': len(avg_files),
                'station_names': [d.name for d in station_dirs]
            }
            
            print(f"{county}: {len(station_dirs)} 個氣象站, {len(avg_files)} 個平均值檔案")
            
        elif empty_county_dir.exists():
            # 空縣市
            county_status[county] = {
                'status': 'empty',
                'stations': 0,
                'avg_files': 0,
                'station_names': []
            }
            
            print(f"{county}(empty): 空縣市資料夾")
            
        else:
            # 遺漏的縣市
            county_status[county] = {
                'status': 'missing',
                'stations': 0,
                'avg_files': 0,
                'station_names': []
            }
            
            print(f"{county}: ❌ 遺漏")
    
    return county_status

def verify_file_formats():
    """驗證檔案格式"""
    print("\n" + "=" * 60)
    print("驗證檔案格式")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 預期的英文表頭
    expected_headers = [
        "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
        "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
        "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
        "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
        "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
        "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
        "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
        "typhoon", "typhoon name"
    ]
    
    format_issues = []
    total_files_checked = 0
    
    # 檢查有資料的縣市
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files']]
    
    for county_dir in county_dirs:
        county_code = county_dir.name
        
        # 檢查氣象站檔案
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*.csv"))
            for csv_file in csv_files:
                total_files_checked += 1
                issues = check_single_file_format(csv_file, expected_headers)
                if issues:
                    format_issues.extend([(csv_file, issue) for issue in issues])
        
        # 檢查平均值檔案
        avg_files = list(county_dir.glob(f"{county_code}_*.csv"))
        for avg_file in avg_files:
            total_files_checked += 1
            issues = check_single_file_format(avg_file, expected_headers)
            if issues:
                format_issues.extend([(avg_file, issue) for issue in issues])
    
    print(f"檢查了 {total_files_checked} 個檔案")
    print(f"發現 {len(format_issues)} 個格式問題")
    
    if format_issues:
        print("\n格式問題詳情：")
        for file_path, issue in format_issues[:10]:  # 只顯示前10個問題
            print(f"  {file_path}: {issue}")
        if len(format_issues) > 10:
            print(f"  ... 還有 {len(format_issues) - 10} 個問題")
    
    return len(format_issues)

def check_single_file_format(file_path, expected_headers):
    """檢查單個檔案格式"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 1:
            issues.append("檔案為空")
            return issues
        
        # 檢查表頭
        first_line = lines[0].strip()
        try:
            reader = csv.reader([first_line])
            headers = next(reader)
            
            if len(headers) != len(expected_headers):
                issues.append(f"表頭欄位數量錯誤：期望{len(expected_headers)}個，實際{len(headers)}個")
            
            for i, (expected, actual) in enumerate(zip(expected_headers, headers)):
                if expected != actual:
                    issues.append(f"表頭第{i+1}欄錯誤：期望'{expected}'，實際'{actual}'")
                    break  # 只報告第一個錯誤
        except:
            issues.append("無法解析表頭")
    
    except Exception as e:
        issues.append(f"讀取檔案失敗：{e}")
    
    return issues

def generate_final_report(county_status, format_issues_count, fixed_files_count):
    """生成最終報告"""
    print("\n" + "=" * 60)
    print("最終完整性報告")
    print("=" * 60)
    
    # 統計縣市狀態
    has_data_count = sum(1 for status in county_status.values() if status['status'] == 'has_data')
    empty_count = sum(1 for status in county_status.values() if status['status'] == 'empty')
    missing_count = sum(1 for status in county_status.values() if status['status'] == 'missing')
    
    # 統計氣象站和檔案數量
    total_stations = sum(status['stations'] for status in county_status.values())
    total_avg_files = sum(status['avg_files'] for status in county_status.values())
    
    print(f"縣市統計：")
    print(f"  有資料的縣市：{has_data_count} 個")
    print(f"  空縣市資料夾：{empty_count} 個")
    print(f"  遺漏的縣市：{missing_count} 個")
    print(f"  總縣市數：{has_data_count + empty_count + missing_count} 個")
    
    print(f"\n檔案統計：")
    print(f"  氣象站總數：{total_stations} 個")
    print(f"  平均值檔案：{total_avg_files} 個")
    print(f"  修正的檔案：{fixed_files_count} 個")
    print(f"  格式問題：{format_issues_count} 個")
    
    print(f"\n完整性評估：")
    if missing_count == 0 and format_issues_count == 0:
        print("✅ 所有縣市和檔案都完整且格式正確！")
    elif missing_count == 0:
        print(f"⚠️ 縣市完整，但有 {format_issues_count} 個檔案格式問題")
    else:
        print(f"❌ 有 {missing_count} 個縣市遺漏，{format_issues_count} 個格式問題")

def main():
    """主函數"""
    print("開始分類修正後的完整修復和驗證")
    
    # 步驟1：修正平均值檔案名稱
    fixed_files_count = fix_average_file_names()
    
    # 步驟2：驗證縣市結構
    county_status = verify_county_structure()
    
    # 步驟3：驗證檔案格式
    format_issues_count = verify_file_formats()
    
    # 步驟4：生成最終報告
    generate_final_report(county_status, format_issues_count, fixed_files_count)

if __name__ == "__main__":
    main()
