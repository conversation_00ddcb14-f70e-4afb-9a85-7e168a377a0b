#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建遺漏的空縣市資料夾
根據台灣完整的縣市列表，創建沒有氣象站資料的空縣市資料夾並標記為(empty)
"""

import os
from pathlib import Path

def create_missing_empty_counties():
    """創建遺漏的空縣市資料夾"""
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 台灣完整的縣市代號列表（22個縣市）
    all_counties = [
        'CHA',  # 彰化縣
        'CYI',  # 嘉義市
        'CYQ',  # 嘉義縣
        'HSZ',  # 新竹縣
        'HUA',  # 花蓮縣
        'ILN',  # 宜蘭縣
        'KEE',  # 基隆市
        'KHH',  # 高雄市
        'KIN',  # 金門縣
        'LIE',  # 連江縣
        'MIA',  # 苗栗縣
        'NTO',  # 南投縣
        'NWT',  # 新北市
        'PEN',  # 澎湖縣
        'PIF',  # 屏東縣
        'TNN',  # 台南市
        'TPE',  # 台北市
        'TTT',  # 台東縣
        'TXG',  # 台中市
        'TYN',  # 桃園市
        'YUN'   # 雲林縣
    ]
    
    # 獲取現有的縣市資料夾
    existing_counties = []
    for item in base_path.iterdir():
        if item.is_dir() and item.name not in ['less_than_24files']:
            # 移除(empty)後綴來獲取縣市代號
            county_code = item.name.replace('(empty)', '')
            existing_counties.append(county_code)
    
    print(f"現有縣市資料夾：{sorted(existing_counties)}")
    print(f"應有縣市總數：{len(all_counties)}")
    print(f"現有縣市總數：{len(existing_counties)}")
    
    # 找出遺漏的縣市
    missing_counties = []
    for county in all_counties:
        if county not in existing_counties:
            missing_counties.append(county)
    
    print(f"遺漏的縣市：{missing_counties}")
    
    # 創建遺漏的空縣市資料夾
    created_count = 0
    for county in missing_counties:
        empty_county_dir = base_path / f"{county}(empty)"
        
        if not empty_county_dir.exists():
            empty_county_dir.mkdir(exist_ok=True)
            print(f"創建空縣市資料夾：{county}(empty)")
            created_count += 1
        else:
            print(f"空縣市資料夾已存在：{county}(empty)")
    
    print(f"\n創建完成：")
    print(f"新創建的空縣市資料夾：{created_count} 個")
    print(f"總縣市資料夾數：{len(existing_counties) + created_count}")
    
    # 驗證最終結果
    final_counties = []
    for item in base_path.iterdir():
        if item.is_dir() and item.name not in ['less_than_24files']:
            county_code = item.name.replace('(empty)', '')
            final_counties.append(county_code)
    
    print(f"最終縣市列表：{sorted(final_counties)}")
    
    if len(final_counties) == len(all_counties):
        print("✅ 所有縣市資料夾都已創建完成！")
    else:
        print(f"⚠️ 還有 {len(all_counties) - len(final_counties)} 個縣市遺漏")
    
    return created_count

def main():
    """主函數"""
    print("=" * 60)
    print("創建遺漏的空縣市資料夾")
    print("=" * 60)
    
    created_count = create_missing_empty_counties()
    
    print(f"\n任務完成！創建了 {created_count} 個空縣市資料夾")

if __name__ == "__main__":
    main()
