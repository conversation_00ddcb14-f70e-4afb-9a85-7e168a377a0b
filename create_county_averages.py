#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建縣市平均值檔案
"""

import os
import csv
import pandas as pd
from pathlib import Path
from datetime import datetime
import numpy as np

# 颱風資料
TYPHOON_DATA = {
    '韋帕': ('2025-07-18', '2025-07-19'),
    '丹娜絲': ('2025-07-05', '2025-07-07'),
    '天兔': ('2024-11-14', '2024-11-16'),
    '康芮': ('2024-10-29', '2024-11-01'),
    '山陀兒': ('2024-09-29', '2024-10-04'),
    '凱米': ('2024-07-22', '2024-07-26'),
    '小犬': ('2023-10-02', '2023-10-06'),
    '海葵': ('2023-09-01', '2023-09-05'),
    '蘇拉': ('2023-08-28', '2023-08-31'),
    '卡努': ('2023-08-01', '2023-08-04'),
    '杜蘇芮': ('2023-07-24', '2023-07-28')
}

# 正確的表頭
CHINESE_HEADERS = [
    "觀測時間(day)", "測站氣壓(hPa)", "海平面氣壓(hPa)", "測站最高氣壓(hPa)", 
    "測站最高氣壓時間(LST)", "測站最低氣壓(hPa)", "測站最低氣壓時間(LST)", 
    "氣溫(℃)", "最高氣溫(℃)", "最高氣溫時間(LST)", "最低氣溫(℃)", 
    "最低氣溫時間(LST)", "露點溫度(℃)", "相對溼度(%)", "最小相對溼度(%)", 
    "最小相對溼度時間(LST)", "風速(m/s)", "風向(360degree)", "最大瞬間風(m/s)", 
    "最大瞬間風風向(360degree)", "最大瞬間風風速時間(LST)", "降水量(mm)", 
    "降水時數(hour)", "最大十分鐘降水量(mm)", "最大十分鐘降水量起始時間(LST)", 
    "最大六十分鐘降水量(mm)", "最大六十分鐘降水量起始時間(LST)", "日照時數(hour)", 
    "日照率(%)", "全天空日射量(MJ/㎡)", "地溫0cm", "地溫5cm", "地溫10cm", 
    "地溫20cm", "地溫30cm", "地溫50cm", "地溫100cm", "颱風", "颱風名稱"
]

ENGLISH_HEADERS = [
    "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
    "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
    "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
    "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
    "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
    "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
    "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
    "typhoon", "typhoon name"
]

def get_typhoon_info(date_str):
    """根據日期判斷是否有颱風及颱風名稱"""
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except:
        return 'N', ''
    
    for typhoon_name, (start_str, end_str) in TYPHOON_DATA.items():
        start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_str, '%Y-%m-%d').date()
        
        if start_date <= date <= end_date:
            return 'Y', typhoon_name
    
    return 'N', ''

def read_csv_data(file_path):
    """讀取CSV檔案資料"""
    try:
        # 跳過前兩行表頭，讀取資料
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) <= 2:
            return []
        
        data_lines = lines[2:]  # 跳過表頭
        data = []
        
        for line in data_lines:
            if line.strip():
                reader = csv.reader([line])
                for row in reader:
                    if len(row) >= 37:  # 確保有足夠的欄位
                        data.append(row)
                    break
        
        return data
    except Exception as e:
        print(f"讀取檔案 {file_path} 時發生錯誤：{e}")
        return []

def calculate_average(values):
    """計算數值平均值"""
    numeric_values = []
    for val in values:
        try:
            if val and val != '/' and val != 'X' and val != '':
                numeric_values.append(float(val))
        except:
            continue
    
    if numeric_values:
        return round(np.mean(numeric_values), 2)
    else:
        return '/'

def create_monthly_average(county_dir, year_month):
    """創建單月平均值檔案"""
    year, month = year_month.split('-')
    output_filename = f"{county_dir.name}_{year}_{month}.csv"
    output_path = county_dir / output_filename
    
    print(f"創建平均值檔案：{output_filename}")
    
    # 收集所有相關檔案的資料
    all_data = {}  # 按日期分組
    
    # 獲取所有氣象站資料夾
    station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
    
    for station_dir in station_dirs:
        csv_files = list(station_dir.glob(f"*-{year}-{month}.csv"))
        
        for csv_file in csv_files:
            data = read_csv_data(csv_file)
            
            for row in data:
                if len(row) >= 37:
                    date = row[0].strip()
                    if date not in all_data:
                        all_data[date] = []
                    all_data[date].append(row)
    
    if not all_data:
        print(f"沒有找到 {year}-{month} 的資料")
        return
    
    # 計算每日平均值
    daily_averages = []
    
    for date in sorted(all_data.keys()):
        date_data = all_data[date]
        
        if not date_data:
            continue
        
        # 初始化平均值行
        avg_row = [date]  # 日期
        
        # 數值欄位索引（需要計算平均值的欄位）
        numeric_indices = [1, 2, 3, 5, 7, 8, 10, 12, 13, 14, 16, 17, 18, 19, 21, 22, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]
        
        # 時間欄位索引（取第一個值）
        time_indices = [4, 6, 9, 11, 15, 20, 24, 26]
        
        # 處理每個欄位
        for i in range(1, 37):  # 從第二個欄位開始（第一個是日期）
            if i in numeric_indices:
                # 數值欄位：計算平均值
                values = [row[i] for row in date_data if len(row) > i]
                avg_value = calculate_average(values)
                avg_row.append(avg_value)
            elif i in time_indices:
                # 時間欄位：取第一個值
                first_value = date_data[0][i] if len(date_data[0]) > i else '/'
                avg_row.append(first_value)
            else:
                # 其他欄位：取第一個值
                first_value = date_data[0][i] if len(date_data[0]) > i else '/'
                avg_row.append(first_value)
        
        # 添加颱風資訊
        typhoon_flag, typhoon_name = get_typhoon_info(date)
        avg_row.append(typhoon_flag)
        avg_row.append(typhoon_name)
        
        daily_averages.append(avg_row)
    
    # 寫入檔案
    try:
        with open(output_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            
            # 寫入表頭
            writer.writerow(CHINESE_HEADERS)
            writer.writerow(ENGLISH_HEADERS)
            
            # 寫入資料
            for row in daily_averages:
                writer.writerow(row)
        
        print(f"成功創建：{output_path}")
        
    except Exception as e:
        print(f"寫入檔案 {output_path} 時發生錯誤：{e}")

def main():
    """主函數"""
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    if not base_path.exists():
        print(f"找不到資料夾：{base_path}")
        return
    
    print("開始創建縣市平均值檔案...")
    
    # 獲取所有非空縣市資料夾
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files']]
    
    for county_dir in county_dirs:
        print(f"\n處理縣市：{county_dir.name}")
        
        # 獲取所有氣象站資料夾
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        
        if not station_dirs:
            continue
        
        # 收集所有年月組合
        year_months = set()
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*.csv"))
            for csv_file in csv_files:
                # 從檔名提取年月
                parts = csv_file.stem.split('-')
                if len(parts) >= 3:
                    year_month = f"{parts[1]}-{parts[2]}"
                    year_months.add(year_month)
        
        # 為每個年月創建平均值檔案
        for year_month in sorted(year_months):
            create_monthly_average(county_dir, year_month)
    
    print("\n縣市平均值檔案創建完成！")

if __name__ == "__main__":
    main()
