#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
標記空的縣市資料夾腳本
將沒有氣象站資料的縣市資料夾名稱加上 (empty) 標記
"""

import os
from pathlib import Path

def mark_empty_county_folders():
    """
    檢查縣市資料夾，將空的資料夾名稱加上 (empty) 標記
    """
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    if not base_path.exists():
        print(f"錯誤：找不到資料夾 {base_path}")
        return
    
    print(f"開始檢查縣市資料夾：{base_path}")
    
    # 縣市代號列表
    county_codes = [
        'NTO', 'TXG', 'TPE', 'TNN', 'TTT', 'CYI', 'CYQ', 
        'KEE', 'ILN', 'PIF', 'CHA', 'NTP', 'HSZ', 'HSC', 
        'TAO', 'PEN', 'HUA', 'MIA', 'KIN', 'YUN', 'KHH'
    ]
    
    empty_count = 0
    non_empty_count = 0
    renamed_count = 0
    
    for county_code in county_codes:
        county_dir = base_path / county_code
        
        if county_dir.exists() and county_dir.is_dir():
            # 檢查資料夾內是否有子資料夾（氣象站資料夾）
            subdirs = [d for d in county_dir.iterdir() if d.is_dir()]
            
            if len(subdirs) == 0:
                # 空資料夾，需要重新命名
                new_name = f"{county_code}(empty)"
                new_path = base_path / new_name
                
                try:
                    county_dir.rename(new_path)
                    print(f"重新命名：{county_code} -> {new_name}")
                    renamed_count += 1
                    empty_count += 1
                except Exception as e:
                    print(f"錯誤：無法重新命名 {county_code}，原因：{e}")
            else:
                print(f"非空資料夾：{county_code} (包含 {len(subdirs)} 個氣象站)")
                non_empty_count += 1
        else:
            print(f"警告：找不到縣市資料夾 {county_code}")
    
    print(f"\n檢查完成！")
    print(f"非空縣市資料夾：{non_empty_count} 個")
    print(f"空縣市資料夾：{empty_count} 個")
    print(f"成功重新命名：{renamed_count} 個")
    
    # 顯示最終的縣市資料夾列表
    print(f"\n最終縣市資料夾列表：")
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and (d.name in county_codes or d.name.endswith('(empty)'))]
    
    for county_dir in sorted(county_dirs, key=lambda x: x.name):
        subdirs = [d for d in county_dir.iterdir() if d.is_dir()]
        status = f"({len(subdirs)} 個氣象站)" if subdirs else "(空)"
        print(f"  - {county_dir.name} {status}")

if __name__ == "__main__":
    mark_empty_county_folders()
