#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
氣象資料處理腳本
1. 修復CSV檔案中文表頭
2. 新增颱風資料欄位
3. 生成縣市平均值檔案
"""

import os
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np

# 颱風資料
TYPHOON_DATA = {
    '韋帕': ('2025-07-18', '2025-07-19'),
    '丹娜絲': ('2025-07-05', '2025-07-07'),
    '天兔': ('2024-11-14', '2024-11-16'),
    '康芮': ('2024-10-29', '2024-11-01'),
    '山陀兒': ('2024-09-29', '2024-10-04'),
    '凱米': ('2024-07-22', '2024-07-26'),
    '小犬': ('2023-10-02', '2023-10-06'),
    '海葵': ('2023-09-01', '2023-09-05'),
    '蘇拉': ('2023-08-28', '2023-08-31'),
    '卡努': ('2023-08-01', '2023-08-04'),
    '杜蘇芮': ('2023-07-24', '2023-07-28')
}

# 正確的中文表頭（從參考檔案獲取）
CORRECT_HEADERS = {
    'chinese': [
        "觀測時間(day)", "測站氣壓(hPa)", "海平面氣壓(hPa)", "測站最高氣壓(hPa)", 
        "測站最高氣壓時間(LST)", "測站最低氣壓(hPa)", "測站最低氣壓時間(LST)", 
        "氣溫(℃)", "最高氣溫(℃)", "最高氣溫時間(LST)", "最低氣溫(℃)", 
        "最低氣溫時間(LST)", "露點溫度(℃)", "相對溼度(%)", "最小相對溼度(%)", 
        "最小相對溼度時間(LST)", "風速(m/s)", "風向(360degree)", "最大瞬間風(m/s)", 
        "最大瞬間風風向(360degree)", "最大瞬間風風速時間(LST)", "降水量(mm)", 
        "降水時數(hour)", "最大十分鐘降水量(mm)", "最大十分鐘降水量起始時間(LST)", 
        "最大六十分鐘降水量(mm)", "最大六十分鐘降水量起始時間(LST)", "日照時數(hour)", 
        "日照率(%)", "全天空日射量(MJ/㎡)", "地溫0cm", "地溫5cm", "地溫10cm", 
        "地溫20cm", "地溫30cm", "地溫50cm", "地溫100cm", "颱風", "颱風名稱"
    ],
    'english': [
        "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
        "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
        "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
        "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
        "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
        "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
        "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
        "typhoon", "typhoon name"
    ]
}

def get_typhoon_info(date_str):
    """
    根據日期判斷是否有颱風及颱風名稱
    """
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except:
        return 'N', ''
    
    for typhoon_name, (start_str, end_str) in TYPHOON_DATA.items():
        start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_str, '%Y-%m-%d').date()
        
        if start_date <= date <= end_date:
            return 'Y', typhoon_name
    
    return 'N', ''

def process_csv_file(file_path):
    """
    處理單個CSV檔案：修復表頭並新增颱風資料
    """
    try:
        # 讀取檔案
        df = pd.read_csv(file_path, encoding='utf-8')
        
        # 檢查是否已經有颱風欄位
        if '颱風' in df.columns:
            print(f"跳過已處理的檔案：{file_path}")
            return True
        
        # 修復表頭
        if len(df.columns) >= 37:  # 原始欄位數量
            # 更新現有欄位名稱
            for i, col in enumerate(CORRECT_HEADERS['chinese'][:len(df.columns)]):
                if i < len(df.columns):
                    df.columns.values[i] = col
        
        # 新增颱風欄位
        df['颱風'] = 'N'
        df['颱風名稱'] = ''
        
        # 處理每一行資料
        for idx, row in df.iterrows():
            date_str = str(row['觀測時間(day)']).strip()
            typhoon_flag, typhoon_name = get_typhoon_info(date_str)
            df.at[idx, '颱風'] = typhoon_flag
            df.at[idx, '颱風名稱'] = typhoon_name
        
        # 重新建立完整的表頭結構
        new_df = pd.DataFrame()
        
        # 第一行：中文表頭
        chinese_row = pd.Series(CORRECT_HEADERS['chinese'], name=0)
        new_df = pd.concat([new_df, chinese_row.to_frame().T], ignore_index=True)
        
        # 第二行：英文表頭
        english_row = pd.Series(CORRECT_HEADERS['english'], name=1)
        new_df = pd.concat([new_df, english_row.to_frame().T], ignore_index=True)
        
        # 資料行
        for idx, row in df.iterrows():
            data_row = []
            for col in CORRECT_HEADERS['chinese']:
                if col in df.columns:
                    data_row.append(row[col])
                else:
                    data_row.append('')
            
            new_row = pd.Series(data_row, name=idx+2)
            new_df = pd.concat([new_df, new_row.to_frame().T], ignore_index=True)
        
        # 設定欄位名稱
        new_df.columns = CORRECT_HEADERS['chinese']
        
        # 儲存檔案
        new_df.to_csv(file_path, index=False, encoding='utf-8')
        print(f"已處理：{file_path}")
        return True
        
    except Exception as e:
        print(f"處理檔案 {file_path} 時發生錯誤：{e}")
        return False

def create_county_average_files():
    """
    為每個縣市創建平均值檔案
    """
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 獲取所有非空縣市資料夾
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files']]
    
    for county_dir in county_dirs:
        print(f"\n處理縣市：{county_dir.name}")
        
        # 獲取所有氣象站資料夾
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        
        if not station_dirs:
            continue
        
        # 按年月分組處理
        year_months = set()
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*.csv"))
            for csv_file in csv_files:
                # 從檔名提取年月
                parts = csv_file.stem.split('-')
                if len(parts) >= 3:
                    year_month = f"{parts[1]}-{parts[2]}"
                    year_months.add(year_month)
        
        # 為每個年月創建平均值檔案
        for year_month in sorted(year_months):
            create_monthly_average(county_dir, year_month, station_dirs)

def create_monthly_average(county_dir, year_month, station_dirs):
    """
    創建單月平均值檔案
    """
    year, month = year_month.split('-')
    output_filename = f"{county_dir.name}_{year}_{month}.csv"
    output_path = county_dir / output_filename
    
    # 收集所有相關檔案的資料
    all_data = []
    
    for station_dir in station_dirs:
        csv_files = list(station_dir.glob(f"*-{year}-{month}.csv"))
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, encoding='utf-8', skiprows=2)  # 跳過表頭行
                if not df.empty:
                    all_data.append(df)
            except Exception as e:
                print(f"讀取檔案 {csv_file} 時發生錯誤：{e}")
    
    if not all_data:
        return
    
    # 合併所有資料
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 按日期分組計算平均值
    daily_averages = []
    
    for date in combined_df['觀測時間(day)'].unique():
        date_data = combined_df[combined_df['觀測時間(day)'] == date]
        
        avg_row = {'觀測時間(day)': date}
        
        # 數值欄位計算平均值
        numeric_columns = [
            '測站氣壓(hPa)', '海平面氣壓(hPa)', '測站最高氣壓(hPa)', '測站最低氣壓(hPa)',
            '氣溫(℃)', '最高氣溫(℃)', '最低氣溫(℃)', '露點溫度(℃)', '相對溼度(%)',
            '最小相對溼度(%)', '風速(m/s)', '風向(360degree)', '最大瞬間風(m/s)',
            '最大瞬間風風向(360degree)', '降水量(mm)', '降水時數(hour)',
            '最大十分鐘降水量(mm)', '最大六十分鐘降水量(mm)', '日照時數(hour)',
            '日照率(%)', '全天空日射量(MJ/㎡)', '地溫0cm', '地溫5cm', '地溫10cm',
            '地溫20cm', '地溫30cm', '地溫50cm', '地溫100cm'
        ]
        
        for col in numeric_columns:
            if col in date_data.columns:
                # 過濾非數值資料
                numeric_values = pd.to_numeric(date_data[col], errors='coerce')
                avg_row[col] = numeric_values.mean() if not numeric_values.isna().all() else '/'
        
        # 時間欄位取第一個值
        time_columns = [
            '測站最高氣壓時間(LST)', '測站最低氣壓時間(LST)', '最高氣溫時間(LST)',
            '最低氣溫時間(LST)', '最小相對溼度時間(LST)', '最大瞬間風風速時間(LST)',
            '最大十分鐘降水量起始時間(LST)', '最大六十分鐘降水量起始時間(LST)'
        ]
        
        for col in time_columns:
            if col in date_data.columns:
                avg_row[col] = date_data[col].iloc[0] if not date_data[col].empty else '/'
        
        # 颱風資料
        typhoon_flag, typhoon_name = get_typhoon_info(str(date))
        avg_row['颱風'] = typhoon_flag
        avg_row['颱風名稱'] = typhoon_name
        
        daily_averages.append(avg_row)
    
    # 創建結果DataFrame
    result_df = pd.DataFrame(daily_averages)
    
    # 確保欄位順序正確
    result_df = result_df.reindex(columns=CORRECT_HEADERS['chinese'], fill_value='/')
    
    # 建立完整的輸出DataFrame（包含表頭）
    output_df = pd.DataFrame()
    
    # 第一行：中文表頭
    chinese_row = pd.Series(CORRECT_HEADERS['chinese'], name=0)
    output_df = pd.concat([output_df, chinese_row.to_frame().T], ignore_index=True)
    
    # 第二行：英文表頭
    english_row = pd.Series(CORRECT_HEADERS['english'], name=1)
    output_df = pd.concat([output_df, english_row.to_frame().T], ignore_index=True)
    
    # 資料行
    for idx, row in result_df.iterrows():
        new_row = pd.Series(row.values, name=idx+2)
        output_df = pd.concat([output_df, new_row.to_frame().T], ignore_index=True)
    
    # 設定欄位名稱
    output_df.columns = CORRECT_HEADERS['chinese']
    
    # 儲存檔案
    output_df.to_csv(output_path, index=False, encoding='utf-8')
    print(f"已創建平均值檔案：{output_path}")

def main():
    """
    主函數
    """
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    print("開始處理氣象資料...")
    
    # 任務1和2：處理所有CSV檔案
    print("\n=== 任務1&2：修復表頭並新增颱風資料 ===")
    
    processed_count = 0
    error_count = 0
    
    # 獲取所有非空縣市資料夾
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files']]
    
    for county_dir in county_dirs:
        print(f"\n處理縣市：{county_dir.name}")
        
        # 獲取所有氣象站資料夾
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*.csv"))
            
            for csv_file in csv_files:
                if process_csv_file(csv_file):
                    processed_count += 1
                else:
                    error_count += 1
    
    print(f"\n表頭修復完成！成功處理：{processed_count} 個檔案，錯誤：{error_count} 個檔案")
    
    # 任務3：創建縣市平均值檔案
    print("\n=== 任務3：創建縣市平均值檔案 ===")
    create_county_average_files()
    
    print("\n所有任務完成！")

if __name__ == "__main__":
    main()
