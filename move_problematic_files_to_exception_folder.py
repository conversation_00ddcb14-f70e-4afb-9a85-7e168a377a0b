#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將有問題的檔案移動到異常資料夾
檢查所有檔案格式，將不符合標準的檔案移動到 exception_files 資料夾
"""

import os
import shutil
from pathlib import Path
import csv
from datetime import datetime

def check_file_format(file_path, expected_headers):
    """檢查檔案格式是否正確"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 1:
            issues.append("檔案為空")
            return issues
        
        # 檢查表頭
        first_line = lines[0].strip()
        try:
            reader = csv.reader([first_line])
            headers = next(reader)
            
            # 檢查欄位數量
            if len(headers) != len(expected_headers):
                issues.append(f"表頭欄位數量錯誤：期望{len(expected_headers)}個，實際{len(headers)}個")
            
            # 檢查第一個欄位是否為英文
            if len(headers) > 0 and headers[0] != expected_headers[0]:
                issues.append(f"第一欄錯誤：期望'{expected_headers[0]}'，實際'{headers[0]}'")
            
            # 檢查是否包含中文字符
            for i, header in enumerate(headers):
                if any('\u4e00' <= char <= '\u9fff' for char in str(header)):
                    issues.append(f"表頭第{i+1}欄包含中文：'{header}'")
                    break
                    
        except Exception as e:
            issues.append(f"無法解析表頭：{e}")
    
    except Exception as e:
        issues.append(f"讀取檔案失敗：{e}")
    
    return issues

def create_exception_folder():
    """創建異常檔案資料夾"""
    base_path = Path("data/raw/weather/agricultural_weather_station")
    exception_path = base_path / "exception_files"
    
    if not exception_path.exists():
        exception_path.mkdir(exist_ok=True)
        print(f"創建異常檔案資料夾：{exception_path}")
    
    return exception_path

def move_problematic_files():
    """移動有問題的檔案到異常資料夾"""
    print("=" * 60)
    print("檢查並移動有問題的檔案到異常資料夾")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    exception_path = create_exception_folder()
    
    # 預期的英文表頭
    expected_headers = [
        "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
        "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
        "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
        "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
        "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
        "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
        "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
        "typhoon", "typhoon name"
    ]
    
    moved_files = []
    total_files_checked = 0
    
    # 檢查有資料的縣市
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files', 'exception_files']]
    
    for county_dir in county_dirs:
        county_code = county_dir.name
        print(f"\n檢查縣市：{county_code}")
        
        # 檢查氣象站檔案
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        for station_dir in station_dirs:
            station_code = station_dir.name
            csv_files = list(station_dir.glob("*.csv"))
            
            for csv_file in csv_files:
                total_files_checked += 1
                issues = check_file_format(csv_file, expected_headers)
                
                if issues:
                    # 創建異常檔案的目標路徑
                    relative_path = csv_file.relative_to(base_path)
                    target_dir = exception_path / relative_path.parent
                    target_dir.mkdir(parents=True, exist_ok=True)
                    target_file = target_dir / csv_file.name
                    
                    # 移動檔案
                    print(f"  移動問題檔案：{relative_path}")
                    print(f"    問題：{issues[0]}")  # 只顯示第一個問題
                    
                    shutil.move(str(csv_file), str(target_file))
                    moved_files.append((str(relative_path), issues))
        
        # 檢查平均值檔案
        avg_files = list(county_dir.glob(f"{county_code}_*.csv"))
        for avg_file in avg_files:
            total_files_checked += 1
            issues = check_file_format(avg_file, expected_headers)
            
            if issues:
                # 創建異常檔案的目標路徑
                relative_path = avg_file.relative_to(base_path)
                target_file = exception_path / avg_file.name
                
                # 移動檔案
                print(f"  移動問題平均值檔案：{relative_path}")
                print(f"    問題：{issues[0]}")  # 只顯示第一個問題
                
                shutil.move(str(avg_file), str(target_file))
                moved_files.append((str(relative_path), issues))
    
    return moved_files, total_files_checked

def create_exception_report(moved_files, total_files_checked):
    """創建異常檔案報告"""
    exception_path = Path("data/raw/weather/agricultural_weather_station/exception_files")
    report_file = exception_path / "exception_files_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("異常檔案報告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"檢查檔案總數：{total_files_checked}\n")
        f.write(f"異常檔案數量：{len(moved_files)}\n")
        f.write(f"異常檔案比例：{len(moved_files)/total_files_checked*100:.2f}%\n\n")
        
        f.write("異常檔案詳情：\n")
        f.write("-" * 50 + "\n")
        
        for file_path, issues in moved_files:
            f.write(f"檔案：{file_path}\n")
            for issue in issues:
                f.write(f"  問題：{issue}\n")
            f.write("\n")
    
    print(f"\n異常檔案報告已生成：{report_file}")

def verify_remaining_files():
    """驗證剩餘檔案的完整性"""
    print("\n" + "=" * 60)
    print("驗證剩餘檔案的完整性")
    print("=" * 60)
    
    base_path = Path("data/raw/weather/agricultural_weather_station")
    
    # 預期的英文表頭
    expected_headers = [
        "ObsTime", "StnPres", "SeaPres", "StnPresMax", "StnPresMaxTime", 
        "StnPresMin", "StnPresMinTime", "Temperature", "T Max", "T Max Time", 
        "T Min", "T Min Time", "Td dew point", "RH", "RHMin", "RHMinTime", 
        "WS", "WD", "WSGust", "WDGust", "WGustTime", "Precp", "PrecpHour", 
        "PrecpMax10", "PrecpMax10Time", "PrecpMax60", "PrecpMax60Time", 
        "SunShine", "SunshineRate", "GloblRad", "TxSoil0cm", "TxSoil5cm", 
        "TxSoil10cm", "TxSoil20cm", "TxSoil30cm", "TxSoil50cm", "TxSoil100cm",
        "typhoon", "typhoon name"
    ]
    
    remaining_issues = 0
    total_remaining_files = 0
    
    # 檢查剩餘的檔案
    county_dirs = [d for d in base_path.iterdir() 
                  if d.is_dir() and not d.name.endswith('(empty)') 
                  and d.name not in ['less_than_24files', 'exception_files']]
    
    for county_dir in county_dirs:
        # 檢查氣象站檔案
        station_dirs = [d for d in county_dir.iterdir() if d.is_dir()]
        for station_dir in station_dirs:
            csv_files = list(station_dir.glob("*.csv"))
            total_remaining_files += len(csv_files)
            
            for csv_file in csv_files:
                issues = check_file_format(csv_file, expected_headers)
                if issues:
                    remaining_issues += 1
        
        # 檢查平均值檔案
        county_code = county_dir.name
        avg_files = list(county_dir.glob(f"{county_code}_*.csv"))
        total_remaining_files += len(avg_files)
        
        for avg_file in avg_files:
            issues = check_file_format(avg_file, expected_headers)
            if issues:
                remaining_issues += 1
    
    print(f"剩餘檔案總數：{total_remaining_files}")
    print(f"剩餘問題檔案：{remaining_issues}")
    
    if remaining_issues == 0:
        print("✅ 所有剩餘檔案格式都正確！")
    else:
        print(f"⚠️ 還有 {remaining_issues} 個檔案有問題")
    
    return total_remaining_files, remaining_issues

def main():
    """主函數"""
    print("開始將有問題的檔案移動到異常資料夾")
    
    # 步驟1：移動有問題的檔案
    moved_files, total_files_checked = move_problematic_files()
    
    # 步驟2：創建異常檔案報告
    create_exception_report(moved_files, total_files_checked)
    
    # 步驟3：驗證剩餘檔案
    total_remaining_files, remaining_issues = verify_remaining_files()
    
    # 最終報告
    print("\n" + "=" * 60)
    print("最終處理結果")
    print("=" * 60)
    print(f"原始檔案總數：{total_files_checked}")
    print(f"移動到異常資料夾：{len(moved_files)} 個檔案")
    print(f"剩餘正常檔案：{total_remaining_files} 個檔案")
    print(f"移動比例：{len(moved_files)/total_files_checked*100:.2f}%")
    
    if remaining_issues == 0:
        print("\n🎉 所有剩餘檔案都符合標準格式！")
        print("✅ 氣象資料整理工作100%完成！")
    else:
        print(f"\n⚠️ 還有 {remaining_issues} 個檔案需要處理")

if __name__ == "__main__":
    main()
